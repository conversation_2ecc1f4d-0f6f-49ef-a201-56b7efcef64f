package com.center.platform.metachat;

import com.center.framework.db.config.SnowFlakeConfig;
import com.center.framework.web.pojo.CommonResult;
import com.center.platform.metachat.util.TencentUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Tag(name = "获取腾讯语音服务")
@RequestMapping("/chat")
@Slf4j
public class GetTokenController {



    @Operation(summary = "获取腾讯语音服务(语音转文字)Credentials")
    @GetMapping (value = "/getTsc")
    public CommonResult<String> getAsrCredentials() {
        return CommonResult.success(TencentUtils.GetTmpCredentials("asr"));
    }

}
