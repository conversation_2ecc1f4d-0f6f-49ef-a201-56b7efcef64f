package com.center.emergency.biz.aisearch.common.listener;

import cn.hutool.json.JSONUtil;
import com.center.emergency.biz.aisearch.common.collector.AiSearchMessageEventCollector;
import com.center.emergency.biz.aisearch.common.enumeration.AiSearchEventType;
import com.center.emergency.biz.aisearch.common.parser.AiSearchStreamParser;
import com.center.emergency.biz.aisearch.common.persistence.AiSearchParsedEvent;
import com.center.emergency.biz.aisearch.pojo.SearchRequest;
import com.center.emergency.biz.aisearch.service.impl.AiSearchServiceImpl;
import com.center.framework.common.context.LoginContextHolder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * AI搜索业务层 Listener：把解析后的事件按业务需求推送前端、落库。
 */
@Slf4j
public class AiSearchSSEListener extends AiSearchGenericSSEListener {

    private final AiSearchServiceImpl service;
    private final SearchRequest searchRequest;
    private final Long sessionId;
    private final Long userMessageId;
    private final StringBuilder builder;
    private final AiSearchMessageEventCollector eventCollector;

    public AiSearchSSEListener(AiSearchStreamParser parser,
                              CountDownLatch latch,
                              SseEmitter emitter,
                              AiSearchServiceImpl service,
                              SearchRequest searchRequest,
                              Long sessionId,
                              Long userMessageId,
                              StringBuilder sharedBuilder) {
        super(emitter, parser, latch);
        this.service = service;
        this.searchRequest = searchRequest;
        this.sessionId = sessionId;
        this.userMessageId = userMessageId;
        this.builder = sharedBuilder;
        this.eventCollector = new AiSearchMessageEventCollector();
    }

    /* ------------- 处理业务事件 ------------- */
    @Override
    protected void handleParsedEvent(AiSearchParsedEvent ev, EventSource es) throws IOException {
        // 收集所有事件（智能合并MESSAGE）
        eventCollector.addEvent(ev);
        log.info("AI搜索事件收集状态: {}", eventCollector.getStatusInfo());
        
        switch (ev.getEventType()) {
            case MESSAGE:
                if (StringUtils.isBlank(ev.getContent())) break;   // 空串不处理
                sendEvent("message",
                        Collections.singletonMap("content", ev.getContent()));
                break;

            case SEARCH_RESULTS:
                // 直接透传搜索结果事件到前端 - 统一格式
                sendEvent("search_results", Collections.singletonMap("content", ev.getContent()));
                break;

            case TOOL_CALL:
                // 直接透传工具调用事件到前端 - 统一格式
                sendEvent("tool_call", Collections.singletonMap("content", ev.getContent()));
                break;

            case TOOL_RESPONSE:
                // 直接透传工具响应事件到前端 - 统一格式
                sendEvent("tool_response", Collections.singletonMap("content", ev.getContent()));
                break;

            case STATUS_UPDATE:
                // 直接透传状态更新事件到前端 - 统一格式
                sendEvent("status_update", Collections.singletonMap("content", ev.getContent()));
                break;

            case COMPLETED:
                // 先发送completed事件到前端，再触发结束流程 - 统一格式
                sendEvent("completed", Collections.singletonMap("content", ev.getContent()));
                finishSearch();
                break;

            case SPAM:
                sendEvent("spam", ev.getContent());
                finishSearch();             // 统一使用finishSearch
                es.cancel();
                break;

            case END:
                finishSearch();
                break;

            case ERROR:
                sendError(ev.getContent());
                finishSearch();             // 统一使用finishSearch
                es.cancel();
                break;

            default:
                log.warn("未识别的AI搜索事件类型: {}", ev.getEventType());
        }
    }

    /* ------------- 私有辅助 ------------- */

    /**
     * 结束搜索：发送end事件，关闭连接
     */
    private void finishSearch() throws IOException {
        try {
            // 1. 发送end事件给前端
            sendEvent("end", searchRequest);
        } catch (Exception e) {
            log.error("发送end事件失败，会话ID: {}", sessionId, e);
        } finally {
            // 2. 关闭连接
            complete();
        }
    }

    /**
     * 重写onClosed方法，在连接关闭后同步保存数据库
     */
    @Override
    public void onClosed(EventSource es) {
        log.info("AI搜索 SSE 连接已关闭，开始同步保存数据库");

        // 1. 先调用父类方法完成基础清理
        super.onClosed(es);

        // 2. 同步保存数据库（连接关闭后）
        saveDataSafely();
    }

    /**
     * 重写onFailure方法，确保异常时也能保存数据
     */
    @Override
    public void onFailure(EventSource es, Throwable t, Response res) {
        log.error("AI搜索 SSE 连接失败: {}", t != null ? t.getMessage() : "未知错误", t);

        // 1. 先保存数据
        saveDataSafely();

        // 2. 再调用父类方法处理异常
        super.onFailure(es, t, res);
    }

    /**
     * 安全保存数据的统一方法
     */
    private void saveDataSafely() {
        try {
            long startTime = System.currentTimeMillis();
            log.info("开始同步保存AI搜索数据，事件状态: {}, sessionId: {}, userMessageId: {}",
                    eventCollector.getStatusInfo(), sessionId, userMessageId);

            // 获取当前用户上下文信息用于调试
            Long tenantId = LoginContextHolder.getLoginUserTenantId();
            Long userId = LoginContextHolder.getLoginUserId();
            log.info("保存AI搜索数据 - 当前用户上下文: tenantId={}, userId={}", tenantId, userId);

            service.saveSearchQuestionAndAnswerWithEvents(sessionId, userMessageId, eventCollector.getMergedEvents());

            long duration = System.currentTimeMillis() - startTime;
            log.info("AI搜索数据保存完成，耗时: {}ms, 会话ID: {}", duration, sessionId);
        } catch (Exception e) {
            log.error("同步保存AI搜索数据失败，会话ID: {}, userMessageId: {}, 详细错误: {}",
                    sessionId, userMessageId, e.getMessage(), e);
            // 不再静默处理异常，记录更详细的错误信息
            log.error("AI搜索数据保存异常堆栈:", e);
        }
    }
} 