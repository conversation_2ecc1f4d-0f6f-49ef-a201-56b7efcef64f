package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum FileStatusEnum implements IEnumerate<String> {
    LOADING("LOADING", "加载中"),
    PARSING("PARSING", "解析中"),
    INDEXING("INDEXING", "索引中"),
    LOADED("LOADED", "加载完成"),
    PROCESSING("PROCESSING", "生成中"),
    PROCESSED("PROCESSED", "生成完成"),
    PROCESS_FAILED("PROCESS_FAILED", "生成失败"),
    LOAD_FAILED("LOAD_FAILED", "加载失败");
    ;


    private String value;
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    // 判断枚举中是否包含某个值
    public static boolean contains(String status) {
        for (FileStatusEnum enumValue : FileStatusEnum.values()) {
            if (enumValue.name().equalsIgnoreCase(status)) {
                return true;
            }
        }
        return false;
    }
}
